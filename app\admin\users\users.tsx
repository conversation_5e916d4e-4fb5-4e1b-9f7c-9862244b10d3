import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../components/admin/AdminLayout';
import AddUserModal from '../../components/admin/AddUserModal';
import UserDetailsModal from '../../components/admin/UserDetailsModal';
import EditUserModal from '../../components/admin/EditUserModal';

interface AdminUser {
  id: number;
  username: string;
  email: string;
  role: string;
  isActive: boolean;
  lastLogin: Date | null;
  createdAt: Date;
  updatedAt: Date;
}

const UsersAdmin = () => {
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<'all' | 'active' | 'inactive'>('all');

  // جلب قائمة المستخدمين
  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('authToken') || 
                   document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

      const response = await fetch('/api/admin/users/list', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUsers(data.users);
        }
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // معالج إضافة مستخدم جديد
  const handleUserAdded = () => {
    fetchUsers();
  };

  // معالج عرض تفاصيل المستخدم
  const handleViewDetails = (user: AdminUser) => {
    setSelectedUser(user);
    setShowDetailsModal(true);
  };

  // معالج تعديل المستخدم
  const handleEditUser = (user: AdminUser) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  // معالج تحديث المستخدم
  const handleUserUpdated = () => {
    fetchUsers();
    setShowEditModal(false);
    setSelectedUser(null);
  };

  // معالج تفعيل/إلغاء تفعيل المستخدم
  const handleToggleStatus = async (user: AdminUser) => {
    if (window.confirm(`هل أنت متأكد من ${user.isActive ? 'إلغاء تفعيل' : 'تفعيل'} المستخدم ${user.username}؟`)) {
      try {
        const token = localStorage.getItem('authToken') ||
                     document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

        const response = await fetch('/api/admin/users/toggle-status', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
          body: JSON.stringify({ userId: user.id })
        });

        const data = await response.json();
        if (data.success) {
          fetchUsers(); // إعادة تحميل القائمة
        } else {
          alert(data.message || 'حدث خطأ أثناء تغيير حالة المستخدم');
        }
      } catch (error) {
        console.error('Error toggling user status:', error);
        alert('حدث خطأ في الاتصال بالخادم');
      }
    }
  };

  // معالج حذف المستخدم
  const handleDeleteUser = async (user: AdminUser) => {
    if (window.confirm(`هل أنت متأكد من حذف المستخدم ${user.username}؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
      try {
        const token = localStorage.getItem('authToken') ||
                     document.cookie.split('; ').find(row => row.startsWith('authToken='))?.split('=')[1];

        const response = await fetch('/api/admin/users/delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          credentials: 'include',
          body: JSON.stringify({ userId: user.id })
        });

        const data = await response.json();
        if (data.success) {
          fetchUsers(); // إعادة تحميل القائمة
        } else {
          alert(data.message || 'حدث خطأ أثناء حذف المستخدم');
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        alert('حدث خطأ في الاتصال بالخادم');
      }
    }
  };

  // تصفية المستخدمين
  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filterActive === 'all' || 
                         (filterActive === 'active' && user.isActive) ||
                         (filterActive === 'inactive' && !user.isActive);
    
    return matchesSearch && matchesFilter;
  });

  if (isLoading) {
    return (
      <AdminLayout title="إدارة المستخدمين">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <Head>
        <title>إدارة المستخدمين - لوحة التحكم</title>
        <meta name="description" content="إدارة مستخدمي النظام" />
      </Head>

      <AdminLayout title="إدارة المستخدمين">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">إدارة المستخدمين</h1>
              <p className="text-gray-600">إدارة وإضافة مستخدمي النظام</p>
            </div>
            <button
              onClick={() => setShowAddUserModal(true)}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              <i className="ri-user-add-line text-lg ml-2"></i>
              إضافة مستخدم جديد
            </button>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* البحث */}
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="البحث في المستخدمين..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <i className="ri-search-line text-gray-400"></i>
                  </div>
                </div>
              </div>

              {/* فلتر الحالة */}
              <div className="sm:w-48">
                <select
                  value={filterActive}
                  onChange={(e) => setFilterActive(e.target.value as 'all' | 'active' | 'inactive')}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                >
                  <option value="all">جميع المستخدمين</option>
                  <option value="active">المستخدمين النشطين</option>
                  <option value="inactive">المستخدمين غير النشطين</option>
                </select>
              </div>
            </div>
          </div>

          {/* Users Table */}
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
              <h3 className="text-lg font-semibold text-gray-800 flex items-center">
                <i className="ri-team-line text-xl ml-2 text-primary"></i>
                قائمة المستخدمين ({filteredUsers.length})
              </h3>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المستخدم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      البريد الإلكتروني
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      آخر تسجيل دخول
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الإنشاء
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center ml-3">
                            <i className="ri-user-line text-primary"></i>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {user.username}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {user.id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {user.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          user.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {user.isActive ? 'نشط' : 'غير نشط'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {user.lastLogin 
                          ? new Date(user.lastLogin).toLocaleString('ar-SA')
                          : 'لم يسجل دخول بعد'
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(user.createdAt).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button
                            onClick={() => handleViewDetails(user)}
                            className="text-blue-600 hover:text-blue-800 transition-colors p-2 rounded-lg hover:bg-blue-50"
                            title="عرض التفاصيل"
                          >
                            <i className="ri-eye-line text-lg"></i>
                          </button>
                          <button
                            onClick={() => handleEditUser(user)}
                            className="text-primary hover:text-primary/80 transition-colors p-2 rounded-lg hover:bg-primary/10"
                            title="تعديل"
                          >
                            <i className="ri-edit-line text-lg"></i>
                          </button>
                          <button
                            onClick={() => handleToggleStatus(user)}
                            className={`transition-colors p-2 rounded-lg ${
                              user.isActive
                                ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50'
                                : 'text-green-600 hover:text-green-800 hover:bg-green-50'
                            }`}
                            title={user.isActive ? 'إلغاء التفعيل' : 'تفعيل'}
                          >
                            <i className={`ri-${user.isActive ? 'pause' : 'play'}-circle-line text-lg`}></i>
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user)}
                            className="text-red-600 hover:text-red-800 transition-colors p-2 rounded-lg hover:bg-red-50"
                            title="حذف"
                          >
                            <i className="ri-delete-bin-line text-lg"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-12">
                <i className="ri-user-line text-4xl text-gray-400 mb-4"></i>
                <p className="text-gray-500">
                  {searchTerm || filterActive !== 'all' 
                    ? 'لا توجد نتائج تطابق البحث' 
                    : 'لا توجد مستخدمين'
                  }
                </p>
              </div>
            )}
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">إجمالي المستخدمين</p>
                  <p className="text-2xl font-bold">{users.length}</p>
                </div>
                <i className="ri-team-line text-3xl text-blue-200"></i>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">المستخدمين النشطين</p>
                  <p className="text-2xl font-bold">{users.filter(u => u.isActive).length}</p>
                </div>
                <i className="ri-user-line text-3xl text-green-200"></i>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">تسجيلات دخول اليوم</p>
                  <p className="text-2xl font-bold">
                    {users.filter(u => 
                      u.lastLogin && 
                      new Date(u.lastLogin).toDateString() === new Date().toDateString()
                    ).length}
                  </p>
                </div>
                <i className="ri-login-box-line text-3xl text-purple-200"></i>
              </div>
            </div>
          </div>
        </div>

        {/* مودال إضافة مستخدم جديد */}
        <AddUserModal
          isOpen={showAddUserModal}
          onClose={() => setShowAddUserModal(false)}
          onUserAdded={handleUserAdded}
        />

        {/* مودال تفاصيل المستخدم */}
        <UserDetailsModal
          isOpen={showDetailsModal}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedUser(null);
          }}
          user={selectedUser}
        />

        {/* مودال تعديل المستخدم */}
        <EditUserModal
          isOpen={showEditModal}
          onClose={() => {
            setShowEditModal(false);
            setSelectedUser(null);
          }}
          user={selectedUser}
          onUserUpdated={handleUserUpdated}
        />
      </AdminLayout>
    </>
  );
};

export default UsersAdmin;
