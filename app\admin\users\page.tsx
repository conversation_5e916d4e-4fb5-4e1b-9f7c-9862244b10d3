'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';

const UsersAdmin = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, []);

  return (
    <>
      <Head>
        <title>إدارة المستخدمين - لوحة التحكم</title>
        <meta name="description" content="إدارة المستخدمين في لوحة التحكم" />
      </Head>

      <AdminLayout title="إدارة المستخدمين">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المستخدمين</h1>
            <p className="text-gray-600">إضافة وتعديل وحذف مستخدمي لوحة التحكم</p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <i className="ri-user-settings-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">قريباً</h3>
            <p className="text-gray-600">صفحة إدارة المستخدمين قيد التطوير</p>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default UsersAdmin;
