'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';

const QuoteRequestsAdmin = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, []);

  return (
    <>
      <Head>
        <title>طلبات التسعير - لوحة التحكم</title>
        <meta name="description" content="إدارة طلبات التسعير في لوحة التحكم" />
      </Head>

      <AdminLayout title="طلبات التسعير">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">طلبات التسعير</h1>
            <p className="text-gray-600">عرض ومتابعة طلبات التسعير من العملاء</p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <i className="ri-file-text-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">قريباً</h3>
            <p className="text-gray-600">صفحة طلبات التسعير قيد التطوير</p>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default QuoteRequestsAdmin;
