'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';

const SubcategoriesAdmin = () => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setLoading(false);
  }, []);

  return (
    <>
      <Head>
        <title>إدارة الفئات الفرعية - لوحة التحكم</title>
        <meta name="description" content="إدارة الفئات الفرعية في لوحة التحكم" />
      </Head>

      <AdminLayout title="إدارة الفئات الفرعية">
        <div className="space-y-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة الفئات الفرعية</h1>
            <p className="text-gray-600">إضافة وتعديل وحذف الفئات الفرعية</p>
          </div>

          <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
            <i className="ri-folder-2-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-lg font-medium text-gray-900 mb-2">قريباً</h3>
            <p className="text-gray-600">صفحة إدارة الفئات الفرعية قيد التطوير</p>
          </div>
        </div>
      </AdminLayout>
    </>
  );
};

export default SubcategoriesAdmin;
