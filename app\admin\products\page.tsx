'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUpload from '../../../components/ImageUpload';
import SafeImage from '../../../components/SafeImage';
import { Category, Subcategory } from '../../../types/mysql-database';
import { ProductWithDetails } from '../../../types/mysql-database';

const ProductsAdmin = () => {
  const [productsList, setProductsList] = useState<ProductWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [categorySubcategories, setCategorySubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductWithDetails | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    titleAr: '',
    description: '',
    descriptionAr: '',
    images: [''],
    price: 0,
    originalPrice: 0,
    available: true,
    categoryId: '',
    subcategoryId: '',
    features: [''],
    featuresAr: [''],
    specifications: [
      { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }
    ],
    isActive: true,
    isFeatured: false
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        // جلب المنتجات
        const productsResponse = await fetch('/api/admin/products');
        if (productsResponse.ok) {
          const productsResult = await productsResponse.json();
          setProductsList(productsResult.data || []);
        } else {
          console.error('Failed to fetch products:', productsResponse.status);
          setProductsList([]);
        }

        // جلب الفئات
        const categoriesResponse = await fetch('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesResult = await categoriesResponse.json();
          setCategories(categoriesResult.data || []);
        } else {
          console.error('Failed to fetch categories:', categoriesResponse.status);
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setProductsList([]);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // جلب الفئات الفرعية عند تغيير الفئة الرئيسية
  useEffect(() => {
    const fetchSubcategories = async () => {
      if (formData.categoryId) {
        try {
          const response = await fetch(`/api/subcategories?categoryId=${formData.categoryId}`);
          if (response.ok) {
            const result = await response.json();
            setCategorySubcategories(result.data || []);
          } else {
            console.error('Failed to fetch subcategories:', response.status);
            setCategorySubcategories([]);
          }
        } catch (error) {
          console.error('Error fetching subcategories:', error);
          setCategorySubcategories([]);
        }
      } else {
        setCategorySubcategories([]);
      }
    };

    fetchSubcategories();
  }, [formData.categoryId]);

  const resetForm = () => {
    setFormData({
      title: '',
      titleAr: '',
      description: '',
      descriptionAr: '',
      images: [''],
      price: 0,
      originalPrice: 0,
      available: true,
      categoryId: '',
      subcategoryId: '',
      features: [''],
      featuresAr: [''],
      specifications: [
        { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }
      ],
      isActive: true,
      isFeatured: false
    });
    setEditingProduct(null);
    setCategorySubcategories([]);
  };

  const openModal = (product?: ProductWithDetails) => {
    if (product) {
      setEditingProduct(product);
      setFormData({
        title: product.title || '',
        titleAr: product.title_ar || '',
        description: product.description || '',
        descriptionAr: product.description_ar || '',
        images: product.images?.length ? product.images.map(img => img.image_url) : [''],
        price: product.price || 0,
        originalPrice: product.original_price || 0,
        available: product.available || false,
        categoryId: product.category_id?.toString() || '',
        subcategoryId: product.subcategory_id?.toString() || '',
        features: product.features?.length ? product.features.map(f => f.feature_text_en) : [''],
        featuresAr: product.features?.length ? product.features.map(f => f.feature_text_ar) : [''],
        specifications: product.specifications?.length ? 
          product.specifications.map(s => ({
            nameEn: s.spec_name_en || '',
            nameAr: s.spec_name_ar || '',
            valueEn: s.spec_value_en || '',
            valueAr: s.spec_value_ar || ''
          })) : 
          [{ nameEn: '', nameAr: '', valueEn: '', valueAr: '' }],
        isActive: product.is_active || false,
        isFeatured: product.is_featured || false
      });
    } else {
      resetForm();
    }
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    resetForm();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : (type === 'number' ? parseFloat(value) || 0 : value)
    }));
  };

  const handleArrayChange = (index: number, value: string, field: 'images' | 'features' | 'featuresAr') => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item, i) => i === index ? value : item)
    }));
  };

  const addArrayItem = (field: 'images' | 'features' | 'featuresAr') => {
    setFormData(prev => ({
      ...prev,
      [field]: [...prev[field], '']
    }));
  };

  const removeArrayItem = (index: number, field: 'images' | 'features' | 'featuresAr') => {
    if (formData[field].length > 1) {
      setFormData(prev => ({
        ...prev,
        [field]: prev[field].filter((_, i) => i !== index)
      }));
    }
  };

  const handleSpecificationChange = (index: number, field: 'nameEn' | 'nameAr' | 'valueEn' | 'valueAr', value: string) => {
    setFormData(prev => ({
      ...prev,
      specifications: prev.specifications.map((spec, i) => 
        i === index ? { ...spec, [field]: value } : spec
      )
    }));
  };

  const addSpecification = () => {
    setFormData(prev => ({
      ...prev,
      specifications: [...prev.specifications, { nameEn: '', nameAr: '', valueEn: '', valueAr: '' }]
    }));
  };

  const removeSpecification = (index: number) => {
    if (formData.specifications.length > 1) {
      setFormData(prev => ({
        ...prev,
        specifications: prev.specifications.filter((_, i) => i !== index)
      }));
    }
  };

  return (
    <>
      <Head>
        <title>إدارة المنتجات - لوحة التحكم</title>
        <meta name="description" content="إدارة المنتجات في لوحة التحكم" />
      </Head>

      <AdminLayout title="إدارة المنتجات">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إدارة المنتجات</h1>
              <p className="text-gray-600">إضافة وتعديل وحذف المنتجات</p>
            </div>
            <button
              onClick={() => openModal()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <i className="ri-add-line"></i>
              إضافة منتج جديد
            </button>
          </div>

          {/* Filters */}
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="ابحث عن منتج..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name_ar || category.name_en}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Products List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-4 text-gray-600">جاري تحميل المنتجات...</p>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المنتج
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الفئة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        السعر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {productsList.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                          لا توجد منتجات
                        </td>
                      </tr>
                    ) : (
                      productsList
                        .filter(product => {
                          const matchesSearch = searchTerm === '' || 
                            (product.title_ar && product.title_ar.toLowerCase().includes(searchTerm.toLowerCase())) ||
                            (product.title && product.title.toLowerCase().includes(searchTerm.toLowerCase()));
                          
                          const matchesCategory = selectedCategory === 'all' || 
                            product.category_id?.toString() === selectedCategory;
                          
                          return matchesSearch && matchesCategory;
                        })
                        .map((product) => (
                          <tr key={product.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10">
                                  {product.images && product.images.length > 0 ? (
                                    <SafeImage
                                      src={product.images[0].image_url}
                                      alt={product.title_ar || product.title || ''}
                                      className="h-10 w-10 rounded-full object-cover"
                                    />
                                  ) : (
                                    <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                      <i className="ri-image-line text-gray-400"></i>
                                    </div>
                                  )}
                                </div>
                                <div className="mr-4">
                                  <div className="text-sm font-medium text-gray-900">
                                    {product.title_ar || product.title}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {product.description_ar || product.description}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {categories.find(cat => cat.id === product.category_id)?.name_ar || 
                               categories.find(cat => cat.id === product.category_id)?.name_en || 
                               'غير محدد'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {product.price ? `${product.price} ريال` : 'غير محدد'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                product.is_active 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {product.is_active ? 'نشط' : 'غير نشط'}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => openModal(product)}
                                  className="text-blue-600 hover:text-blue-900"
                                >
                                  <i className="ri-edit-line"></i>
                                </button>
                                <button
                                  onClick={() => {/* handleDelete(product.id) */}}
                                  className="text-red-600 hover:text-red-900"
                                >
                                  <i className="ri-delete-bin-line"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </AdminLayout>
    </>
  );
};

export default ProductsAdmin;
